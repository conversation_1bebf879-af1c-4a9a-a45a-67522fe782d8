<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>玫瑰花绽放 - Three.js</title>
    <style>
        body { margin: 0; background: #000; overflow: hidden; }
        canvas { display: block; }
        #info {
            position: absolute;
            top: 10px;
            left: 10px;
            color: white;
            font-family: Arial, sans-serif;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div id="info">玫瑰花绽放动画 - 点击鼠标重新开始</div>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script>
        let scene, camera, renderer;
        let rose;
        let animationTime = 0;
        let blooming = true;
        
        function init() {
            // 创建场景
            scene = new THREE.Scene();
            scene.background = new THREE.Color(0x000011);
            
            // 创建相机
            camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
            camera.position.set(0, 2, 8);
            camera.lookAt(0, 0, 0);
            
            // 创建渲染器
            renderer = new THREE.WebGLRenderer({ antialias: true });
            renderer.setSize(window.innerWidth, window.innerHeight);
            renderer.shadowMap.enabled = true;
            renderer.shadowMap.type = THREE.PCFSoftShadowMap;
            document.body.appendChild(renderer.domElement);
            
            // 添加光照
            const ambientLight = new THREE.AmbientLight(0x404040, 0.4);
            scene.add(ambientLight);
            
            const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
            directionalLight.position.set(5, 10, 5);
            directionalLight.castShadow = true;
            scene.add(directionalLight);
            
            const pointLight = new THREE.PointLight(0xff69b4, 0.8, 100);
            pointLight.position.set(0, 5, 0);
            scene.add(pointLight);
            
            // 创建玫瑰花
            createRose();
            
            // 添加事件监听
            window.addEventListener('resize', onWindowResize);
            document.addEventListener('click', resetAnimation);
            
            animate();
        }
        
        function createRose() {
            rose = new THREE.Group();
            
            // 花瓣材质
            const petalMaterial = new THREE.MeshPhongMaterial({
                color: 0xff1493,
                transparent: true,
                opacity: 0.9,
                side: THREE.DoubleSide
            });
            
            // 花瓣数量
            const petalCount = 24;
            const layers = 4;
            
            for (let layer = 0; layer < layers; layer++) {
                const layerPetals = Math.floor(petalCount / layers);
                const layerRadius = 0.5 + layer * 0.3;
                const layerHeight = layer * 0.2;
                
                for (let i = 0; i < layerPetals; i++) {
                    const angle = (i / layerPetals) * Math.PI * 2;
                    const petal = createPetal(petalMaterial, layerRadius, layerHeight, angle, layer);
                    petal.scale.set(0.1, 0.1, 0.1); // 初始状态为闭合
                    rose.add(petal);
                }
            }
            
            // 花心
            const centerGeometry = new THREE.SphereGeometry(0.3, 16, 16);
            const centerMaterial = new THREE.MeshPhongMaterial({ color: 0xffff00 });
            const center = new THREE.Mesh(centerGeometry, centerMaterial);
            center.position.y = 0.5;
            center.scale.set(0.1, 0.1, 0.1);
            rose.add(center);
            
            // 茎
            const stemGeometry = new THREE.CylinderGeometry(0.1, 0.15, 4, 8);
            const stemMaterial = new THREE.MeshPhongMaterial({ color: 0x228b22 });
            const stem = new THREE.Mesh(stemGeometry, stemMaterial);
            stem.position.y = -2;
            rose.add(stem);
            
            // 叶子
            for (let i = 0; i < 3; i++) {
                const leaf = createLeaf();
                leaf.position.y = -1 - i * 0.8;
                leaf.position.x = (i % 2 === 0 ? 0.5 : -0.5);
                leaf.rotation.z = (i % 2 === 0 ? 0.3 : -0.3);
                leaf.scale.set(0.1, 0.1, 0.1);
                rose.add(leaf);
            }
            
            scene.add(rose);
        }
        
        function createPetal(material, radius, height, angle, layer) {
            const shape = new THREE.Shape();
            const petalLength = radius * 1.5;
            const petalWidth = radius * 0.8;
            
            // 创建花瓣形状
            shape.moveTo(0, 0);
            shape.quadraticCurveTo(petalWidth * 0.5, petalLength * 0.3, petalWidth * 0.3, petalLength * 0.7);
            shape.quadraticCurveTo(0, petalLength, -petalWidth * 0.3, petalLength * 0.7);
            shape.quadraticCurveTo(-petalWidth * 0.5, petalLength * 0.3, 0, 0);
            
            const extrudeSettings = {
                depth: 0.05,
                bevelEnabled: true,
                bevelSegments: 2,
                steps: 2,
                bevelSize: 0.02,
                bevelThickness: 0.02
            };
            
            const geometry = new THREE.ExtrudeGeometry(shape, extrudeSettings);
            const petal = new THREE.Mesh(geometry, material);
            
            // 设置位置和旋转
            petal.position.x = Math.cos(angle) * radius;
            petal.position.z = Math.sin(angle) * radius;
            petal.position.y = height;
            petal.rotation.y = angle;
            petal.rotation.x = -Math.PI / 6 + layer * 0.1;
            petal.rotation.z = Math.sin(angle * 2) * 0.2;
            
            petal.userData = { 
                initialScale: 0.1, 
                targetScale: 1, 
                delay: layer * 0.3 + Math.random() * 0.2,
                layer: layer 
            };
            
            return petal;
        }
        
        function createLeaf() {
            const shape = new THREE.Shape();
            shape.moveTo(0, 0);
            shape.quadraticCurveTo(0.3, 0.4, 0.2, 0.8);
            shape.quadraticCurveTo(0, 1, -0.2, 0.8);
            shape.quadraticCurveTo(-0.3, 0.4, 0, 0);
            
            const extrudeSettings = {
                depth: 0.02,
                bevelEnabled: true,
                bevelSegments: 1,
                steps: 1,
                bevelSize: 0.01,
                bevelThickness: 0.01
            };
            
            const geometry = new THREE.ExtrudeGeometry(shape, extrudeSettings);
            const material = new THREE.MeshPhongMaterial({ color: 0x228b22 });
            return new THREE.Mesh(geometry, material);
        }
        
        function animate() {
            requestAnimationFrame(animate);
            
            animationTime += 0.016;
            
            // 更新花瓣绽放动画
            rose.children.forEach((child, index) => {
                if (child.userData && child.userData.initialScale !== undefined) {
                    const delay = child.userData.delay;
                    const bloomSpeed = 2;
                    const progress = Math.max(0, Math.min(1, (animationTime - delay) * bloomSpeed));
                    
                    if (progress > 0) {
                        const easeProgress = easeOutElastic(progress);
                        const scale = child.userData.initialScale + 
                                     (child.userData.targetScale - child.userData.initialScale) * easeProgress;
                        child.scale.set(scale, scale, scale);
                        
                        // 添加轻微的摆动
                        child.rotation.z += Math.sin(animationTime * 2 + index) * 0.001;
                    }
                } else if (child.material && child.material.color.getHex() === 0xffff00) {
                    // 花心动画
                    const centerProgress = Math.max(0, Math.min(1, (animationTime - 1.5) * 1.5));
                    const centerScale = 0.1 + centerProgress * 0.9;
                    child.scale.set(centerScale, centerScale, centerScale);
                } else if (child.material && child.material.color.getHex() === 0x228b22) {
                    // 茎和叶子动画
                    const stemProgress = Math.max(0, Math.min(1, animationTime * 0.8));
                    const stemScale = 0.1 + stemProgress * 0.9;
                    child.scale.set(stemScale, stemScale, stemScale);
                }
            });
            
            // 整体旋转
            rose.rotation.y += 0.005;
            
            renderer.render(scene, camera);
        }
        
        function easeOutElastic(x) {
            const c4 = (2 * Math.PI) / 3;
            return x === 0 ? 0 : x === 1 ? 1 : Math.pow(2, -10 * x) * Math.sin((x * 10 - 0.75) * c4) + 1;
        }
        
        function resetAnimation() {
            animationTime = 0;
            rose.children.forEach(child => {
                if (child.userData && child.userData.initialScale !== undefined) {
                    child.scale.set(child.userData.initialScale, child.userData.initialScale, child.userData.initialScale);
                } else if (child.material) {
                    const color = child.material.color.getHex();
                    if (color === 0xffff00 || color === 0x228b22) {
                        child.scale.set(0.1, 0.1, 0.1);
                    }
                }
            });
        }
        
        function onWindowResize() {
            camera.aspect = window.innerWidth / window.innerHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(window.innerWidth, window.innerHeight);
        }
        
        init();
    </script>
</body>
</html>